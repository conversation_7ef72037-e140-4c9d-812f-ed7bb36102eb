PODS:
  - Flutter (1.0.0)
  - flutter_secure_storage (6.0.0):
    - Flutter
  - MTBBarcodeScanner (5.0.11)
  - nearby_service (0.0.1):
    - Flutter
    - FlutterMacOS
  - passkeys_ios (0.0.1):
    - Flutter
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - sign_in_with_apple (0.0.1):
    - Flutter
  - ua_client_hints (1.4.1):
    - Flutter

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - nearby_service (from `.symlinks/plugins/nearby_service/darwin`)
  - passkeys_ios (from `.symlinks/plugins/passkeys_ios/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - ua_client_hints (from `.symlinks/plugins/ua_client_hints/ios`)

SPEC REPOS:
  trunk:
    - MTBBarcodeScanner

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  nearby_service:
    :path: ".symlinks/plugins/nearby_service/darwin"
  passkeys_ios:
    :path: ".symlinks/plugins/passkeys_ios/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  ua_client_hints:
    :path: ".symlinks/plugins/ua_client_hints/ios"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  nearby_service: 89cfcb10c1b5a1a7f3807449b07ca1399134b66e
  passkeys_ios: fdae8c06e2178a9fcb9261a6cb21fb9a06a81d53
  qr_code_scanner: bb67d64904c3b9658ada8c402e8b4d406d5d796e
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  ua_client_hints: aeabd123262c087f0ce151ef96fa3ab77bfc8b38

PODFILE CHECKSUM: 60d7867f0061b077d517737900ac115bff769a00

COCOAPODS: 1.16.2
