import 'package:flutter/material.dart';
import 'package:flutter_gen_ai_chat_ui/flutter_gen_ai_chat_ui.dart';
import 'package:get_it/get_it.dart';
 

class ChatDetailsScreen extends StatefulWidget {
  const ChatDetailsScreen({super.key});

  @override
  State<ChatDetailsScreen> createState() => _ChatDetailsScreenState();
}

class _ChatDetailsScreenState extends State<ChatDetailsScreen> {
  final _controller = ChatMessagesController();
  final _currentUser = ChatUser(id: 'user', firstName: 'User');
  final _aiUser = ChatUser(id: 'ai', firstName: 'AI Assistant');
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('AI Chat')),
      body: SafeArea(
        child: AiChatWidget(
          // Required parameters
          currentUser: _currentUser,
          aiUser: _aiUser,
          controller: _controller,
          onSendMessage: _handleSendMessage,

          // Optional parameters
          loadingConfig: LoadingConfig(isLoading: _isLoading),
          inputOptions: InputOptions(
            decoration: InputDecoration(
              hintText: 'Ask me anything...',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.all(10),
            ),

            sendOnEnter: true,
          ),
          welcomeMessageConfig: WelcomeMessageConfig(
            title: 'Welcome to AI Chat',
            questionsSectionTitle: 'Try asking me:',
          ),
          exampleQuestions: [
            ExampleQuestion(question: "What can you help me with?"),
            ExampleQuestion(question: "Tell me about your features"),
          ],
        ),
      ),
    );
  }

  Future<void> _handleSendMessage(ChatMessage message) async {
    setState(() => _isLoading = true);

    try {
      // Your AI service logic here
      _controller.addMessage(message);

      ///await Future.delayed(Duration(seconds: 1)); // Simulating API call
      // final result = await GetIt.instance<MlxFlutter>().generate(message.text);
      // print(result);
      // Add AI response
      // _controller.addMessage(
      //   ChatMessage(text: result!, user: _aiUser, createdAt: DateTime.now()),
      // );
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
