import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen_ai_chat_ui/flutter_gen_ai_chat_ui.dart';
import 'package:toii_social/cubit/post/user_post/user_post_cubit.dart';
import 'package:toii_social/cubit/post/user_post/user_post_state.dart';
import 'package:toii_social/locator/locator.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/screen/home/<USER>/home_item_post_widget.dart';
import 'package:toii_social/screen/user_profile/widget/user_profile_nft_tab.dart';
import 'package:toii_social/screen/user_profile/widget/user_profile_repost_card.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class UserProfilePostList extends StatefulWidget {
  const UserProfilePostList({super.key, this.user});
  final PostUserModel? user;
  @override
  State<UserProfilePostList> createState() => _UserProfilePostListState();
}

class _UserProfilePostListState extends State<UserProfilePostList> {
  int _tabIndex = 0;
  final tabs = ["Posts", "Repost", "Collected NFT"];
  late final UserPostCubit _userPostCubit;

  @override
  void initState() {
    super.initState();
    _userPostCubit = serviceLocator<UserPostCubit>();
    _loadInitialData();
  }

  void _loadInitialData() {
    if (widget.user?.id != null) {
      _userPostCubit.getUserPosts(widget.user!.id);
    }
  }

  void _handleTabChange(int index) {
    setState(() => _tabIndex = index);
    if (widget.user?.id == null) return;

    if (index == 0) {
      _userPostCubit.getUserPosts(widget.user!.id);
    } else if (index == 1) {
      _userPostCubit.getUserReposts(widget.user!.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tabs
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: List.generate(tabs.length, (i) {
              final selected = i == _tabIndex;
              return GestureDetector(
                onTap: () => _handleTabChange(i),
                child: Padding(
                  padding: const EdgeInsets.only(right: 24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        tabs[i],
                        style:
                            selected
                                ? titleLarge.copyWith(
                                  color: themeData.neutral800,
                                  fontWeight: FontWeight.bold,
                                )
                                : titleLarge.copyWith(
                                  color: themeData.neutral400,
                                ),
                      ),
                      if (selected)
                        Container(
                          margin: const EdgeInsets.only(top: 4),
                          height: 3,
                          width: 28,
                          decoration: BoxDecoration(
                            color: themeData.primaryGreen500,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                    ],
                  ),
                ),
              );
            }),
          ),
        ),
        const SizedBox(height: 8),

        // Content based on selected tab
        BlocBuilder<UserPostCubit, UserPostState>(
          bloc: _userPostCubit,
          builder: (context, state) {
            // Posts Tab
            if (_tabIndex == 0) {
              if (state.postStatus.isLoading) {
                return const Center(child: LoadingWidget());
              } else if (state.postStatus.isLoaded && state.posts != null) {
                final posts = state.posts!.posts;
                if (posts.isEmpty) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 24.0),
                      child: Text('No posts found'),
                    ),
                  );
                }
                return Column(
                  children:
                      posts
                          .map(
                            (post) => HomeItemPostWidget(
                              post: post,
                              isNotOnTap: false,
                              isShowActionMore: true,
                            ),
                          )
                          .toList(),
                );
              } else if (state.postStatus.isError) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 24.0),
                    child: Text('Error: ${state.errorMessage}'),
                  ),
                );
              }
              // Initial state
              return const Center(child: LoadingWidget());
            }
            // Reposts Tab
            else if (_tabIndex == 1) {
              if (state.repostStatus.isLoading) {
                return const Center(child: LoadingWidget());
              } else if (state.repostStatus.isLoaded && state.reposts != null) {
                final reposts = state.reposts!.posts;
                if (reposts.isEmpty) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 24.0),
                      child: Text('No reposts found'),
                    ),
                  );
                }
                return Column(
                  children:
                      reposts
                          .map((post) => UserProfileRepostCard(post: post))
                          .toList(),
                );
              } else if (state.repostStatus.isError) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 24.0),
                    child: Text('Error: ${state.errorMessage}'),
                  ),
                );
              }
              // Initial state
              return const Center(child: LoadingWidget());
            }
            // NFT Tab
            else {
              return const UserProfileNftTab();
            }
          },
        ),
      ],
    );
  }
}
