import 'package:flutter/material.dart';

class BaseTabBar extends StatelessWidget {
  final List<String> tabTitles; // Danh sách tiêu đề tab
  final List<Widget> tabContents; // Danh sách nội dung của từng tab
  final Color? tabBarColor; // <PERSON><PERSON><PERSON> nền của TabBar
  final Color? selectedTabColor; // <PERSON>àu của tab được chọn
  final Color? unselectedTabColor; // Màu của tab không được chọn
  final TextStyle? tabTextStyle; // Style chữ của tab
  final double? tabBarHeight; // Chiều cao của TabBar
  final double? tabContentHeight; // Chiều cao của TabBarView

  const BaseTabBar({
    super.key,
    required this.tabTitles,
    required this.tabContents,
    this.tabBarColor,
    this.selectedTabColor,
    this.unselectedTabColor,
    this.tabTextStyle,
    this.tabBarHeight,
    this.tabContentHeight,
  });

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: tabTitles.length,
      child: Column(
        mainAxisSize: MainAxisSize.min, // Chỉ chiếm không gian cần thiết
        children: [
          // TabBar
          Container(
            height: tabBarHeight ?? 48.0,
            color: tabBarColor ?? Colors.white,
            child: TabBar(
              isScrollable:
                  true, // Cho phép tabs nằm liền kề nhau thay vì expanded
              tabAlignment: TabAlignment.start, // Căn tabs về phía bên trái
              labelColor: selectedTabColor ?? Theme.of(context).primaryColor,
              unselectedLabelColor: unselectedTabColor ?? Colors.grey,
              labelStyle:
                  tabTextStyle ??
                  const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              unselectedLabelStyle:
                  tabTextStyle ??
                  const TextStyle(fontWeight: FontWeight.normal, fontSize: 16),
              indicatorColor:
                  selectedTabColor ?? Theme.of(context).primaryColor,
              tabs: tabTitles.map((title) => Tab(text: title)).toList(),
            ),
          ),
          // TabBarView
          SizedBox(
            height: tabContentHeight ?? 200.0, // Chiều cao nội dung tab
            child: TabBarView(children: tabContents),
          ),
        ],
      ),
    );
  }
}
